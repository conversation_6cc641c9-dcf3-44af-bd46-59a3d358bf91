"""Planning and task management tools for CatchUp v1."""

from __future__ import annotations

import uuid
import os
from datetime import datetime
from typing import List, Optional, Dict, Any, Annotated
from pathlib import Path
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from catchup_v1.state import CatchUpV1State, Task


@tool
def create_plan(
    tasks: List[Dict[str, Any]],
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Create a comprehensive plan by breaking down the user request into actionable tasks.
    
    Args:
        tasks: List of task dictionaries with keys:
            - content: str - Description of the task
            - priority: str - Priority level (low, medium, high, critical)
            - estimated_duration: int - Estimated duration in seconds
            - tools_required: List[str] - Tools needed for this task
            - dependencies: List[str] - Task IDs this task depends on (optional)
    
    Returns:
        Command to update the state with the new plan
    """
    
    # Convert task dictionaries to Task objects
    plan_tasks = []
    current_time = datetime.now().isoformat()
    
    for i, task_data in enumerate(tasks):
        task = Task(
            id=str(uuid.uuid4()),
            content=task_data["content"],
            status="pending",
            priority=task_data.get("priority", "medium"),
            estimated_duration=task_data.get("estimated_duration"),
            actual_duration=None,
            dependencies=task_data.get("dependencies", []),
            tools_required=task_data.get("tools_required", []),
            created_at=current_time,
            started_at=None,
            completed_at=None,
            error_message=None
        )
        plan_tasks.append(task)
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    updated_metrics = {
        **current_metrics,
        "total_tasks": len(plan_tasks),
        "completed_tasks": 0,
        "failed_tasks": 0
    }
    
    # Update conversation phase
    updated_phase = {
        "phase": "planning",
        "confidence": 0.9,
        "context": {"plan_created_at": current_time, "total_tasks": len(plan_tasks)}
    }
    
    return Command(
        update={
            "current_plan": plan_tasks,
            "progress_metrics": updated_metrics,
            "conversation_phase": updated_phase,
            "messages": [
                ToolMessage(
                    content=f"Created comprehensive plan with {len(plan_tasks)} tasks. Ready to begin execution.",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def update_task_status(
    task_id: str,
    status: str,
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    error_message: Optional[str] = None,
    actual_duration: Optional[int] = None,
) -> Command:
    """Update the status of a specific task in the current plan.
    
    Args:
        task_id: ID of the task to update
        status: New status (pending, in_progress, completed, failed)
        error_message: Error message if status is failed
        actual_duration: Actual duration in seconds if task is completed
    
    Returns:
        Command to update the task status in state
    """
    
    current_plan = state.get("current_plan", [])
    current_time = datetime.now().isoformat()
    
    # Find and update the task
    updated_plan = []
    task_found = False
    
    for task in current_plan:
        if task["id"] == task_id:
            task_found = True
            updated_task = {**task}
            updated_task["status"] = status
            
            if status == "in_progress" and not task.get("started_at"):
                updated_task["started_at"] = current_time
            elif status in ["completed", "failed"]:
                updated_task["completed_at"] = current_time
                if actual_duration:
                    updated_task["actual_duration"] = actual_duration
                if error_message:
                    updated_task["error_message"] = error_message
            
            updated_plan.append(updated_task)
        else:
            updated_plan.append(task)
    
    if not task_found:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content=f"Error: Task with ID {task_id} not found in current plan.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    completed_count = sum(1 for task in updated_plan if task["status"] == "completed")
    failed_count = sum(1 for task in updated_plan if task["status"] == "failed")
    
    updated_metrics = {
        **current_metrics,
        "completed_tasks": completed_count,
        "failed_tasks": failed_count
    }
    
    # Calculate average task duration
    completed_tasks_with_duration = [
        task for task in updated_plan 
        if task["status"] == "completed" and task.get("actual_duration")
    ]
    if completed_tasks_with_duration:
        avg_duration = sum(task["actual_duration"] for task in completed_tasks_with_duration) / len(completed_tasks_with_duration)
        updated_metrics["average_task_duration"] = avg_duration
    
    # Update active task ID
    active_task_id = None
    if status == "in_progress":
        active_task_id = task_id
    elif status in ["completed", "failed"] and state.get("active_task_id") == task_id:
        # Find next pending task
        for task in updated_plan:
            if task["status"] == "pending":
                # Check if dependencies are met
                dependencies_met = True
                for dep_id in task.get("dependencies", []):
                    dep_task = next((t for t in updated_plan if t["id"] == dep_id), None)
                    if not dep_task or dep_task["status"] != "completed":
                        dependencies_met = False
                        break
                
                if dependencies_met:
                    active_task_id = task["id"]
                    break
    
    return Command(
        update={
            "current_plan": updated_plan,
            "progress_metrics": updated_metrics,
            "active_task_id": active_task_id,
            "messages": [
                ToolMessage(
                    content=f"Updated task {task_id} status to {status}. Progress: {completed_count}/{len(updated_plan)} tasks completed.",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def get_current_plan(
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Get the current plan with task statuses and progress information.
    
    Returns:
        Command with current plan information
    """
    
    current_plan = state.get("current_plan", [])
    progress_metrics = state.get("progress_metrics", {})
    active_task_id = state.get("active_task_id")
    
    if not current_plan:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content="No current plan exists. Create a plan first using the create_plan tool.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
    
    # Format plan summary
    plan_summary = []
    plan_summary.append(f"Current Plan Summary ({len(current_plan)} tasks):")
    plan_summary.append(f"Progress: {progress_metrics.get('completed_tasks', 0)}/{progress_metrics.get('total_tasks', 0)} completed")
    
    if progress_metrics.get("failed_tasks", 0) > 0:
        plan_summary.append(f"Failed: {progress_metrics['failed_tasks']} tasks")
    
    plan_summary.append("\nTasks:")
    
    for i, task in enumerate(current_plan, 1):
        status_emoji = {
            "pending": "⏳",
            "in_progress": "🔄", 
            "completed": "✅",
            "failed": "❌"
        }.get(task["status"], "❓")
        
        active_indicator = " (ACTIVE)" if task["id"] == active_task_id else ""
        priority_indicator = f" [{task['priority'].upper()}]" if task.get("priority") != "medium" else ""
        
        plan_summary.append(f"{i}. {status_emoji} {task['content']}{priority_indicator}{active_indicator}")
        
        if task.get("error_message"):
            plan_summary.append(f"   Error: {task['error_message']}")
    
    return Command(
        update={
            "messages": [
                ToolMessage(
                    content="\n".join(plan_summary),
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def save_plan_to_markdown(
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    filename: Optional[str] = None,
    directory: str = "plans"
) -> Command:
    """Save the current plan to a Markdown file on the filesystem.

    This is a testing utility that saves the current plan in Markdown format
    to the filesystem for inspection and debugging purposes.

    Args:
        filename: Optional custom filename (without extension). If not provided,
                 uses timestamp-based naming.
        directory: Directory to save the plan file (default: "plans")

    Returns:
        Command with success/error message
    """

    current_plan = state.get("current_plan", [])
    progress_metrics = state.get("progress_metrics", {})
    active_task_id = state.get("active_task_id")
    user_context = state.get("user_context", {})
    session_config = state.get("session_config", {})

    if not current_plan:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content="No current plan exists to save. Create a plan first using the create_plan tool.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )

    try:
        # Create directory if it doesn't exist
        plan_dir = Path(directory)
        plan_dir.mkdir(exist_ok=True)

        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = session_config.get("session_id", "unknown")
            filename = f"plan_{session_id}_{timestamp}"

        # Ensure .md extension
        if not filename.endswith('.md'):
            filename += '.md'

        file_path = plan_dir / filename

        # Generate Markdown content
        markdown_content = _generate_plan_markdown(
            current_plan,
            progress_metrics,
            active_task_id,
            user_context,
            session_config
        )

        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content=f"✅ Plan saved successfully to: {file_path.absolute()}",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )

    except Exception as e:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content=f"❌ Failed to save plan: {str(e)}",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )


def _generate_plan_markdown(
    current_plan: List[Dict[str, Any]],
    progress_metrics: Dict[str, Any],
    active_task_id: Optional[str],
    user_context: Dict[str, Any],
    session_config: Dict[str, Any]
) -> str:
    """Generate Markdown content for the plan.

    Args:
        current_plan: List of task dictionaries
        progress_metrics: Progress tracking metrics
        active_task_id: ID of currently active task
        user_context: User context information
        session_config: Session configuration

    Returns:
        Formatted Markdown string
    """

    lines = []

    # Header
    lines.append("# CatchUp Agent Plan")
    lines.append("")
    lines.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    lines.append(f"**Session ID:** {session_config.get('session_id', 'N/A')}")
    lines.append("")

    # User Context
    if user_context:
        lines.append("## User Context")
        lines.append("")
        if user_context.get("user_id"):
            lines.append(f"- **User ID:** {user_context['user_id']}")
        if user_context.get("email_address"):
            lines.append(f"- **Email:** {user_context['email_address']}")
        if user_context.get("location"):
            location = user_context["location"]
            lines.append(f"- **Location:** {location.get('latitude', 'N/A')}, {location.get('longitude', 'N/A')}")
        if user_context.get("current_session_start"):
            lines.append(f"- **Session Start:** {user_context['current_session_start']}")
        lines.append("")

    # Progress Overview
    lines.append("## Progress Overview")
    lines.append("")
    total_tasks = progress_metrics.get("total_tasks", len(current_plan))
    completed_tasks = progress_metrics.get("completed_tasks", 0)
    failed_tasks = progress_metrics.get("failed_tasks", 0)

    lines.append(f"- **Total Tasks:** {total_tasks}")
    lines.append(f"- **Completed:** {completed_tasks}")
    lines.append(f"- **Failed:** {failed_tasks}")
    lines.append(f"- **Remaining:** {total_tasks - completed_tasks - failed_tasks}")

    if total_tasks > 0:
        completion_percentage = (completed_tasks / total_tasks) * 100
        lines.append(f"- **Progress:** {completion_percentage:.1f}%")

    lines.append("")

    # Tasks
    lines.append("## Tasks")
    lines.append("")

    for i, task in enumerate(current_plan, 1):
        # Status emoji and indicators
        status_emoji = {
            "pending": "⏳",
            "in_progress": "🔄",
            "completed": "✅",
            "failed": "❌"
        }.get(task.get("status", "pending"), "❓")

        active_indicator = " **(ACTIVE)**" if task.get("id") == active_task_id else ""
        priority_indicator = f" `{task.get('priority', 'medium').upper()}`" if task.get("priority") != "medium" else ""

        # Task header
        lines.append(f"### {i}. {status_emoji} {task.get('content', 'No description')}{priority_indicator}{active_indicator}")
        lines.append("")

        # Task details
        lines.append("**Details:**")
        lines.append(f"- **ID:** `{task.get('id', 'N/A')}`")
        lines.append(f"- **Status:** {task.get('status', 'pending')}")
        lines.append(f"- **Priority:** {task.get('priority', 'medium')}")

        if task.get("estimated_duration"):
            lines.append(f"- **Estimated Duration:** {task['estimated_duration']} seconds")

        if task.get("actual_duration"):
            lines.append(f"- **Actual Duration:** {task['actual_duration']} seconds")

        if task.get("tools_required"):
            tools_list = ", ".join(f"`{tool}`" for tool in task["tools_required"])
            lines.append(f"- **Tools Required:** {tools_list}")

        if task.get("dependencies"):
            deps_list = ", ".join(f"`{dep}`" for dep in task["dependencies"])
            lines.append(f"- **Dependencies:** {deps_list}")

        if task.get("created_at"):
            lines.append(f"- **Created:** {task['created_at']}")

        if task.get("started_at"):
            lines.append(f"- **Started:** {task['started_at']}")

        if task.get("completed_at"):
            lines.append(f"- **Completed:** {task['completed_at']}")

        if task.get("error_message"):
            lines.append(f"- **Error:** {task['error_message']}")

        lines.append("")

    # Session Configuration
    if session_config:
        lines.append("## Session Configuration")
        lines.append("")
        for key, value in session_config.items():
            lines.append(f"- **{key.replace('_', ' ').title()}:** {value}")
        lines.append("")

    # Footer
    lines.append("---")
    lines.append("*Generated by CatchUp Agent v1 - Testing Utility*")

    return "\n".join(lines)

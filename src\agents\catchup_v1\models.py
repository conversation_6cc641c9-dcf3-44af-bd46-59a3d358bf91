from typing import List
from pydantic import BaseModel, Field


class PlanTask(BaseModel):
    """Task definition for planning"""
    content: str = Field(description="Clear description of what needs to be done")
    priority: str = Field(description="Priority level: low, medium, high, or critical", default="medium")
    estimated_duration: int = Field(description="Estimated time in seconds", default=30)
    tools_required: List[str] = Field(description="Array of tool names needed", default_factory=list)
    dependencies: List[int] = Field(description="Array of task indices this task depends on", default_factory=list)


class Plan(BaseModel):
    """Plan to follow in future"""
    tasks: List[PlanTask] = Field(
        description="Array of tasks to execute, should be in sorted order"
    )
from catchup_v3.sub_agent import _create_task_tool, SubAgent
from catchup_v3.llm_model import get_default_model

from catchup_v3.state import BuilderState
from typing import Sequence, Union, Callable, Any, TypeVar, Type, Optional
from langchain_core.tools import BaseTool
from langchain_core.language_models import LanguageModelLike

from langgraph.prebuilt import create_react_agent

from catchup_v3.internal_tools import write_todos,write_file, read_file, ls, edit_file

StateSchema = TypeVar("StateSchema", bound=BuilderState)
StateSchemaType = Type[StateSchema]

base_prompt = """You have access to a number of standard tools

## `write_todos`

You have access to the `write_todos` tools to help you manage and plan tasks. Use these tools VERY frequently to ensure that you are tracking your tasks and giving the user visibility into your progress.
These tools are also EXTREMELY helpful for planning tasks, and for breaking down larger complex tasks into smaller steps. If you do not use this tool when planning, you may forget to do important tasks - and that is unacceptable.

It is critical that you mark todos as completed as soon as you are done with a task. Do not batch up multiple tasks before marking them as completed.
## `task`

- When doing web search, prefer to use the `task` tool in order to reduce context usage."""


def build_plan_and_execute_agent(
    tools: Sequence[Union[BaseTool, Callable, dict[str, Any]]],
    instructions: str,
    model: Optional[Union[str, LanguageModelLike]] = None,
    subagents: list[SubAgent] = None,
    state_schema: Optional[StateSchemaType] = None,
):
    """Create a deep agent.

    This agent will by default have access to a tool to write todos (write_todos),
    and then four file editing tools: write_file, ls, read_file, edit_file.

    Args:
        tools: The additional tools the agent should have access to.
        instructions: The additional instructions the agent should have. Will go in
            the system prompt.
        model: The model to use.
        subagents: The subagents to use. Each subagent should be a dictionary with the
            following keys:
                - `name`
                - `description` (used by the main agent to decide whether to call the sub agent)
                - `prompt` (used as the system prompt in the subagent)
                - (optional) `tools`
        state_schema: The schema of the deep agent. Should subclass from DeepAgentState
    """
    prompt = instructions + base_prompt
    built_in_tools = [write_todos, write_file, read_file, ls, edit_file]
    if model is None:
        model = get_default_model()
    state_schema = state_schema or BuilderState
    task_tool = _create_task_tool(
        list(tools) + built_in_tools,
        instructions,
        subagents or [],
        model,
        state_schema
    )
    all_tools = built_in_tools + list(tools) + [task_tool]
    return create_react_agent(
        model,
        prompt=prompt,
        tools=all_tools,
        state_schema=state_schema,
    )


def build_explicit_plan_execute_agent(
    tools: Sequence[Union[BaseTool, Callable, dict[str, Any]]],
    instructions: str,
    model: Optional[Union[str, LanguageModelLike]] = None,
    subagents: list[SubAgent] = None,
    state_schema: Optional[StateSchemaType] = None,
    enable_replanning: bool = True,
):
    """Create explicit plan-execute-replan agent with segregated nodes.
    
    Same interface as build_plan_and_execute_agent but with explicit:
    - Planner node (generates initial plan)
    - Executor node (executes tasks one by one) 
    - Replanner node (modifies plan based on results)
    """
    
    # Internal planner/executor/replanner nodes as ReAct agents
    def _create_planner_node():
        planner_prompt = f"""You are a strategic planner. Break down user requests into actionable tasks.
        
        {instructions}
        
        Create a structured plan using write_todos. Be specific and actionable."""
        
        return create_react_agent(
            model,
            prompt=planner_prompt,
            tools=[write_todos] + built_in_tools,
            state_schema=state_schema
        )
    
    def _create_executor_node():
        executor_prompt = f"""You are a task executor. Execute one task at a time from the plan.
        
        {instructions}
        
        Use the task tool to delegate to subagents when appropriate."""
        
        return create_react_agent(
            model,
            prompt=executor_prompt,
            tools=all_tools,  # Full tool access including task delegation
            state_schema=state_schema
        )
        
    def _create_replanner_node():
        replanner_prompt = f"""You are a replanner. Analyze completed tasks and update the remaining plan.
        
        {instructions}
        
        Modify the plan based on results using write_todos."""
        
        return create_react_agent(
            model,
            prompt=replanner_prompt,
            tools=[write_todos] + built_in_tools,
            state_schema=state_schema
        )
    
    # Build the explicit graph
    workflow = StateGraph(state_schema or BuilderState)
    workflow.add_node("planner", _create_planner_node)
    workflow.add_node("executor", _create_executor_node)
    if enable_replanning:
        workflow.add_node("replanner", _create_replanner_node)
    
    # Add routing logic
    workflow.add_edge(START, "planner")
    # ... routing between nodes
    
    return workflow.compile()


